---
import Layout from '../layouts/Layout.astro';
import ProjectCard from '../components/ProjectCard.astro';
import { getEntry, getCollection } from 'astro:content';

const homepageContent = await getEntry('homepage', 'main');
const { hero, about } = homepageContent.data;

// Get latest portfolio projects for preview
const portfolioProjects = await getCollection('portfolio');
const featuredProjects = portfolioProjects
  .sort((a, b) => b.data.publishDate.getTime() - a.data.publishDate.getTime())
  .slice(0, 3);
---

<Layout title="Nob Hokleng | Software Developer & System Architect">
  <!-- Hero Section -->
  <section class="hero relative min-h-screen flex items-center justify-center overflow-hidden" role="banner">
    <!-- Background with improved gradients -->
    <div class="absolute inset-0 bg-gradient-to-br from-light via-white to-gray-50"></div>
    <div class="absolute inset-0 bg-gradient-to-tr from-primary/5 via-transparent to-accent/5"></div>
    
    <!-- Animated background elements -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-primary/10 to-accent/10 rounded-full blur-3xl animate-pulse"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-secondary/10 to-primary/10 rounded-full blur-3xl animate-pulse"></div>
    </div>
    
    <div class="container mx-auto px-5 max-w-6xl relative z-10 py-20">
      <div class="text-center">
        <!-- Status badge -->
        <div class="status-badge inline-flex items-center gap-2 bg-gradient-to-r from-green-500/10 to-green-600/10 border border-green-500/20 text-green-700 px-4 py-2 rounded-full font-medium mb-8 text-sm">
          <span class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
          Available for new opportunities
        </div>
        
        <!-- Main heading with enhanced typography -->
        <h1 class="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold font-heading mb-6 leading-tight">
          <span class="block bg-gradient-to-r from-secondary via-primary to-secondary bg-clip-text text-transparent" set:html={hero.headline}></span>
        </h1>
        
        <h2 class="text-xl sm:text-2xl lg:text-3xl text-primary mb-8 font-semibold max-w-4xl mx-auto leading-relaxed">
          {hero.subheadline}
        </h2>
        
        <p class="text-lg sm:text-xl max-w-3xl mx-auto mb-12 text-text/80 leading-relaxed">
          {hero.description}
        </p>
        
        <!-- Enhanced highlights with better spacing -->
        <div class="hero-highlights grid grid-cols-2 lg:grid-cols-4 gap-4 mb-12 max-w-4xl mx-auto">
          {hero.highlights.map((highlight) => (
            <div class="highlight-item group flex flex-col items-center gap-3 p-5 bg-white/70 backdrop-blur-sm rounded-2xl shadow-lg border border-white/50 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 hover:bg-white/90">
              <span class="text-2xl group-hover:scale-110 transition-transform duration-300">{highlight.icon}</span>
              <span class="font-semibold text-text text-sm text-center leading-tight">{highlight.label}</span>
            </div>
          ))}
        </div>
        
        <!-- Enhanced CTA buttons -->
        <div class="cta-buttons flex justify-center gap-6 flex-col sm:flex-row">
          <a href={hero.primaryCTA.url} class="group btn-primary inline-flex items-center justify-center gap-3 px-8 py-4 bg-gradient-to-r from-primary to-secondary text-white rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 text-lg" aria-label="View my portfolio projects">
            {hero.primaryCTA.text}
            <svg class="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
            </svg>
          </a>
          <a href={hero.secondaryCTA.url} class="group btn-secondary inline-flex items-center justify-center gap-3 px-8 py-4 border-2 border-primary text-primary rounded-2xl font-semibold hover:bg-primary hover:text-white transition-all duration-300 hover:-translate-y-1 text-lg backdrop-blur-sm bg-white/50" aria-label="Get in touch with me">
            {hero.secondaryCTA.text}
            <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
          </a>
        </div>
        
        <!-- Scroll indicator -->
        <div class="scroll-indicator absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <svg class="w-6 h-6 text-primary/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
          </svg>
        </div>
      </div>
    </div>
  </section>

  <!-- About Section -->
  <section id="about" class="about py-24 bg-white" role="main">
    <div class="container mx-auto px-5 max-w-6xl">
      <h2 class="section-title text-4xl font-bold text-center mb-12 font-heading relative">
        About Me
        <span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 -mb-2 w-20 h-1 bg-gradient-to-r from-primary to-accent rounded"></span>
      </h2>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-15 items-start">
        <div class="about-text space-y-6">
          <p class="text-lg font-medium text-secondary leading-relaxed">
            {about.openingLine}
          </p>
          {about.mainContent.map((paragraph) => (
            <p class="text-base leading-relaxed text-text">
              {paragraph}
            </p>
          ))}
        </div>
          
          <div class="experience-highlights mt-8">
            <h3 class="text-xl font-bold text-secondary mb-5 font-heading">Experience Highlights</h3>
            <ul class="space-y-2">
              <li class="flex items-center">
                <span class="text-primary mr-3 font-bold">✓</span>
                <span class="text-text">Designed and implemented scalable backend systems</span>
              </li>
              <li class="flex items-center">
                <span class="text-primary mr-3 font-bold">✓</span>
                <span class="text-text">Built high-performance APIs serving thousands of requests</span>
              </li>
              <li class="flex items-center">
                <span class="text-primary mr-3 font-bold">✓</span>
                <span class="text-text">Implemented DevOps practices and CI/CD pipelines</span>
              </li>
              <li class="flex items-center">
                <span class="text-primary mr-3 font-bold">✓</span>
                <span class="text-text">Worked with cloud platforms and containerization</span>
              </li>
              <li class="flex items-center">
                <span class="text-primary mr-3 font-bold">✓</span>
                <span class="text-text">Mentored team members and led technical initiatives</span>
              </li>
            </ul>
          </div>
        </div>
        
        <div class="skills-section bg-light p-10 rounded-2xl shadow-lg">
          <h3 class="text-2xl font-bold text-secondary mb-8 text-center font-heading">Technical Skills</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div class="skill-category bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-1">
              <h4 class="text-base font-semibold text-primary mb-4 font-heading">Backend Development</h4>
              <ul class="space-y-1 text-sm text-text">
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Java & Spring Framework</li>
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Node.js & Express</li>
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Python & FastAPI</li>
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>RESTful APIs & GraphQL</li>
              </ul>
            </div>
            <div class="skill-category bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-1">
              <h4 class="text-base font-semibold text-primary mb-4 font-heading">System Architecture</h4>
              <ul class="space-y-1 text-sm text-text">
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Microservices Architecture</li>
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Event-Driven Systems</li>
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Database Design</li>
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Caching Strategies</li>
              </ul>
            </div>
            <div class="skill-category bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-1">
              <h4 class="text-base font-semibold text-primary mb-4 font-heading">DevOps & Infrastructure</h4>
              <ul class="space-y-1 text-sm text-text">
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Docker & Kubernetes</li>
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>AWS & Cloud Services</li>
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>CI/CD Pipelines</li>
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Monitoring & Logging</li>
              </ul>
            </div>
            <div class="skill-category bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-1">
              <h4 class="text-base font-semibold text-primary mb-4 font-heading">Development Practices</h4>
              <ul class="space-y-1 text-sm text-text">
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Test-Driven Development</li>
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Code Review & Quality</li>
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Agile Methodologies</li>
                <li class="flex items-center"><span class="text-accent mr-2 font-bold">•</span>Technical Documentation</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Portfolio Section -->
  <section id="portfolio" class="portfolio py-24 bg-light relative overflow-hidden" role="region" aria-labelledby="portfolio-title">
    <div class="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5"></div>
    <div class="container mx-auto px-5 max-w-6xl relative z-10">
      <h2 id="portfolio-title" class="section-title text-4xl font-bold text-center mb-12 font-heading relative">
        Portfolio
        <span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 -mb-2 w-20 h-1 bg-gradient-to-r from-primary to-accent rounded"></span>
      </h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" role="list">
        <ProjectCard 
          title="E-Commerce Platform Backend"
          description="Scalable microservices architecture for high-traffic e-commerce platform. Built with modern technologies to handle thousands of concurrent users and process millions of transactions."
          tags={["Java", "Spring Boot", "Microservices"]}
        />
        <ProjectCard 
          title="Real-time Analytics System"
          description="Event-driven system processing millions of events daily. Provides real-time insights and analytics with low-latency data processing and visualization."
          tags={["Node.js", "Apache Kafka", "Redis"]}
        />
        <ProjectCard 
          title="DevOps Infrastructure"
          description="Automated CI/CD pipeline and infrastructure as code implementation. Streamlined deployment processes with monitoring, logging, and automated scaling."
          tags={["Docker", "Kubernetes", "AWS"]}
        />
      </div>
    </div>
  </section>
</Layout> 