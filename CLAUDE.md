# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

NobiSite is a professional portfolio website built with modern Astro.js + Tailwind CSS stack. The project demonstrates comprehensive planning with extensive documentation and follows professional development practices. **Migration from static HTML/CSS/JS to Astro.js has been completed successfully.**

## Development Commands

```bash
# Development server
npm run dev         # Astro dev server with hot reload at localhost:4321
npm start          # Same as dev

# Build and deployment
npm run build      # Production build with Astro (outputs to dist/)
npm run preview    # Serves built files with Astro preview

# Type checking and validation
npm run check      # TypeScript checking for Astro components

# Package management (uses pnpm)
pnpm install       # Install dependencies
pnpm dev           # Start development server
pnpm build         # Build for production
```

## Architecture Overview

- **Framework**: Astro.js with TypeScript for static site generation
- **Styling**: Tailwind CSS with custom design system
- **Content**: MDX with frontmatter-based collections (portfolio projects)
- **Components**: Astro components for <PERSON>er, <PERSON>er, ProjectCard
- **Type Safety**: Full TypeScript integration with strict null checks
- **Build Output**: Static files optimized for production deployment

### Content Collections Structure
```
src/content/
├── config.ts           # Zod schemas for content validation
├── portfolio/          # Project case studies (MDX)
├── homepage/           # Homepage content (MDX)
├── about/              # About page content (MDX)
└── resources/          # Resource links and bookmarks (MDX)
```

### Content Collection Schemas
- **Portfolio**: title, publishDate, problem, solution, technologies, role, results, heroImage, repoUrl?, liveUrl?
- **Homepage**: hero section, about section, highlights, CTAs
- **About**: sections with headings, content, and subsections
- **Resources**: title, url, description, category, tags

### Component Architecture
```
src/
├── components/         # Reusable Astro components (Header, Footer, ProjectCard)
├── layouts/           # Page layouts (Layout.astro)
├── pages/             # File-based routing
│   ├── index.astro    # Homepage
│   ├── about.astro    # About page
│   ├── contact.astro  # Contact page
│   ├── resume.astro   # Resume page
│   ├── resources.astro # Resources page
│   └── portfolio/     # Portfolio pages
│       ├── index.astro # Portfolio listing
│       └── [slug].astro # Dynamic portfolio project pages
├── styles/            # Global CSS + Tailwind
└── utils/             # Utility functions
```

## Design System

### Tailwind Configuration
- **Primary**: #3a86ff (blue)
- **Secondary**: #0a2463 (dark blue)  
- **Accent**: #ff9e00 (orange)
- **Light**: #f8fafc, **Text**: #334155, **Dark**: #1e293b
- **Fonts**: Montserrat (headings), Poppins (body), Fira Code (mono)
- **Custom Spacing**: 15 (3.75rem) added for consistent layout

### Path Aliases
- `@/*` maps to `src/*` for clean imports

## Content Management

### Portfolio Schema
- Required: title, publishDate, problem, solution, technologies, role, results, heroImage
- Optional: repoUrl, liveUrl

### Page Structure
- **Homepage**: Hero section with highlights, CTAs, and about preview
- **About**: Structured sections with headings, content, and subsections
- **Portfolio**: Project listing + dynamic slug-based project pages
- **Resources**: Categorized links with tags for filtering
- **Resume**: Interactive resume with download capability
- **Contact**: Contact form with social links

## Build Process

Astro.js handles:
- Static site generation with file-based routing
- Asset optimization and hashing
- TypeScript compilation
- Tailwind CSS processing
- MDX content processing for portfolio projects

## Development Notes

### Migration Status
✅ **Migration Complete!** - Successfully migrated from static HTML/CSS/JS to modern Astro.js stack
- All pages converted to Astro components
- Portfolio content migrated to MDX collections
- Build system working perfectly
- TypeScript integration complete

### Quality Standards
- Target: 95+ Lighthouse performance score
- WCAG 2.1 AA accessibility compliance  
- TypeScript strict mode enabled
- Modern ES2022 target

### Documentation
Comprehensive project docs in `docs/` folder covering planning, technical specs, and migration guides.