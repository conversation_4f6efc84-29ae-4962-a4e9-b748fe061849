---
// Performance monitoring component for Core Web Vitals
// This component tracks and reports performance metrics
---

<script is:inline>
  // Core Web Vitals monitoring
  (function() {
    // Only run in production and if supported
    if (typeof window === 'undefined' || window.location.hostname === 'localhost') {
      return;
    }

    // Performance metrics storage
    const metrics = {};
    
    // Report metric to analytics (placeholder for future analytics integration)
    function reportMetric(name, value, rating) {
      metrics[name] = { value, rating };
      
      // Log to console in development
      if (window.location.hostname === 'localhost') {
        console.log(`Performance Metric - ${name}:`, { value, rating });
      }
      
      // TODO: Send to analytics service when Task #4 (Analytics Integration) is implemented
      // Example: gtag('event', name, { value, rating });
    }

    // Largest Contentful Paint (LCP)
    function measureLCP() {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          const value = lastEntry.startTime;
          
          let rating = 'good';
          if (value > 4000) rating = 'poor';
          else if (value > 2500) rating = 'needs-improvement';
          
          reportMetric('LCP', value, rating);
        });
        
        observer.observe({ entryTypes: ['largest-contentful-paint'] });
      }
    }

    // First Input Delay (FID)
    function measureFID() {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            const value = entry.processingStart - entry.startTime;
            
            let rating = 'good';
            if (value > 300) rating = 'poor';
            else if (value > 100) rating = 'needs-improvement';
            
            reportMetric('FID', value, rating);
          });
        });
        
        observer.observe({ entryTypes: ['first-input'] });
      }
    }

    // Cumulative Layout Shift (CLS)
    function measureCLS() {
      if ('PerformanceObserver' in window) {
        let clsValue = 0;
        let sessionValue = 0;
        let sessionEntries = [];
        
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          
          entries.forEach((entry) => {
            if (!entry.hadRecentInput) {
              const firstSessionEntry = sessionEntries[0];
              const lastSessionEntry = sessionEntries[sessionEntries.length - 1];
              
              if (sessionValue && 
                  entry.startTime - lastSessionEntry.startTime < 1000 &&
                  entry.startTime - firstSessionEntry.startTime < 5000) {
                sessionValue += entry.value;
                sessionEntries.push(entry);
              } else {
                sessionValue = entry.value;
                sessionEntries = [entry];
              }
              
              if (sessionValue > clsValue) {
                clsValue = sessionValue;
                
                let rating = 'good';
                if (clsValue > 0.25) rating = 'poor';
                else if (clsValue > 0.1) rating = 'needs-improvement';
                
                reportMetric('CLS', clsValue, rating);
              }
            }
          });
        });
        
        observer.observe({ entryTypes: ['layout-shift'] });
      }
    }

    // First Contentful Paint (FCP)
    function measureFCP() {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (entry.name === 'first-contentful-paint') {
              const value = entry.startTime;
              
              let rating = 'good';
              if (value > 3000) rating = 'poor';
              else if (value > 1800) rating = 'needs-improvement';
              
              reportMetric('FCP', value, rating);
            }
          });
        });
        
        observer.observe({ entryTypes: ['paint'] });
      }
    }

    // Time to First Byte (TTFB)
    function measureTTFB() {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (entry.entryType === 'navigation') {
              const value = entry.responseStart - entry.requestStart;
              
              let rating = 'good';
              if (value > 800) rating = 'poor';
              else if (value > 600) rating = 'needs-improvement';
              
              reportMetric('TTFB', value, rating);
            }
          });
        });
        
        observer.observe({ entryTypes: ['navigation'] });
      }
    }

    // Initialize all measurements
    function initPerformanceMonitoring() {
      measureLCP();
      measureFID();
      measureCLS();
      measureFCP();
      measureTTFB();
      
      // Report page load time
      window.addEventListener('load', () => {
        const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
        let rating = 'good';
        if (loadTime > 3000) rating = 'poor';
        else if (loadTime > 1500) rating = 'needs-improvement';
        
        reportMetric('Page Load Time', loadTime, rating);
      });
    }

    // Start monitoring when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initPerformanceMonitoring);
    } else {
      initPerformanceMonitoring();
    }

    // Expose metrics for debugging
    window.performanceMetrics = metrics;
  })();
</script>
