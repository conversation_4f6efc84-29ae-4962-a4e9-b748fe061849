---
import { Image } from 'astro:assets';

interface Props {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  class?: string;
  loading?: 'lazy' | 'eager';
  priority?: boolean;
  sizes?: string;
  quality?: number;
  format?: 'webp' | 'avif' | 'png' | 'jpg' | 'jpeg';
}

const {
  src,
  alt,
  width = 800,
  height = 600,
  class: className = '',
  loading = 'lazy',
  priority = false,
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  quality = 80,
  format = 'webp'
} = Astro.props;

// Determine loading strategy based on priority
const loadingStrategy = priority ? 'eager' : loading;

// Generate responsive image sizes
const responsiveWidths = [320, 640, 768, 1024, 1280, 1536];
const srcSet = responsiveWidths
  .filter(w => w <= width)
  .map(w => `${src}?w=${w}&q=${quality}&f=${format} ${w}w`)
  .join(', ');
---

<!-- Optimized Image with lazy loading and responsive sizes -->
<Image
  src={src}
  alt={alt}
  width={width}
  height={height}
  loading={loadingStrategy}
  class={`optimized-image ${className}`}
  sizes={sizes}
  quality={quality}
  format={format}
  {...priority && { fetchpriority: 'high' }}
/>

<style>
  .optimized-image {
    /* Prevent layout shift */
    display: block;
    width: 100%;
    height: auto;
    
    /* Optimize rendering */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    
    /* Smooth loading transition */
    transition: opacity 0.3s ease-in-out;
  }
  
  /* Loading state */
  .optimized-image[loading="lazy"] {
    opacity: 0;
  }
  
  .optimized-image[loading="lazy"].loaded {
    opacity: 1;
  }
</style>

<script>
  // Enhanced lazy loading with intersection observer
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          
          // Add loaded class when image loads
          img.addEventListener('load', () => {
            img.classList.add('loaded');
          });
          
          // Stop observing this image
          observer.unobserve(img);
        }
      });
    }, {
      // Start loading when image is 50px away from viewport
      rootMargin: '50px 0px',
      threshold: 0.01
    });
    
    // Observe all lazy images
    document.querySelectorAll('img[loading="lazy"]').forEach(img => {
      imageObserver.observe(img);
    });
  }
</script>
