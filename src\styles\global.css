@tailwind base;
@tailwind components;
@tailwind utilities;

/* Critical CSS - Above the fold styles */
/* Base styles for layout structure */
body {
  margin: 0;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #2b2d42;
  line-height: 1.6;
  background-color: #ffffff;
  /* Optimize font rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

main {
  flex-grow: 1;
}

/* Custom scrolling for smooth navigation */
html {
  scroll-behavior: smooth;
}

/* Performance optimizations */
* {
  /* Optimize box-sizing for better layout performance */
  box-sizing: border-box;
}

/* Optimize images for performance */
img {
  /* Prevent layout shift */
  height: auto;
  /* Optimize image rendering */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* Lazy loading support */
img[loading="lazy"] {
  /* Ensure smooth loading transition */
  transition: opacity 0.3s ease-in-out;
}

/* Optimize animations for performance */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Animation keyframes */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 15px rgba(255, 158, 0, 0.2);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 4px 20px rgba(255, 158, 0, 0.4);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4px 15px rgba(255, 158, 0, 0.2);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
} 